const { chromium } = require('playwright');

async function testPageNavigation() {
  console.log('🚀 [导航测试] 开始页面导航测试...');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000  // 每个操作间隔2秒
  });
  
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  
  const page = await context.newPage();
  
  // 要测试的页面路由
  const routes = [
    { path: '/', name: '首页' },
    { path: '/prompt', name: '故事创意输入页' },
    { path: '/generating', name: '内容生成页' },
    { path: '/bible', name: '故事圣经页' },
    { path: '/bible/1', name: '故事圣经详情页' },
    { path: '/cockpit/1', name: '创作驾驶舱' },
    { path: '/reading/1/1', name: '章节阅读页' }
  ];
  
  try {
    for (const route of routes) {
      console.log(`\n🌐 [导航测试] 测试页面: ${route.name} (${route.path})`);
      
      try {
        // 访问页面
        await page.goto(`http://localhost:3000${route.path}`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // 检查页面是否正常加载
        const title = await page.title();
        console.log(`📄 [导航测试] 页面标题: ${title}`);
        
        // 截图保存
        const fileName = `page-${route.name.replace(/[\/\s]/g, '-')}.png`;
        await page.screenshot({ path: fileName });
        console.log(`📸 [导航测试] 截图已保存: ${fileName}`);
        
        // 检查页面是否有错误
        const errorElements = await page.locator('[class*="error"], .error, [data-testid*="error"]').count();
        if (errorElements > 0) {
          console.log(`⚠️ [导航测试] 发现 ${errorElements} 个错误元素`);
        } else {
          console.log(`✅ [导航测试] 页面加载正常，无错误元素`);
        }
        
        // 查找并测试页面上的交互元素
        const buttons = await page.locator('button:visible').count();
        const inputs = await page.locator('input:visible').count();
        const links = await page.locator('a:visible').count();
        
        console.log(`📊 [导航测试] 页面元素统计: ${buttons}个按钮, ${inputs}个输入框, ${links}个链接`);
        
        // 测试第一个可见按钮（如果存在）
        if (buttons > 0) {
          try {
            const firstButton = page.locator('button:visible').first();
            const buttonText = await firstButton.textContent();
            await firstButton.click();
            console.log(`🔘 [导航测试] 成功点击按钮: "${buttonText}"`);
            await page.waitForTimeout(2000);
          } catch (error) {
            console.log(`❌ [导航测试] 按钮点击失败: ${error.message}`);
          }
        }
        
      } catch (error) {
        console.log(`❌ [导航测试] 页面访问失败: ${route.name} - ${error.message}`);
        
        // 即使失败也截图记录
        try {
          await page.screenshot({ path: `error-${route.name.replace(/[\/\s]/g, '-')}.png` });
        } catch (screenshotError) {
          console.log(`❌ [导航测试] 截图失败: ${screenshotError.message}`);
        }
      }
    }
    
    console.log('\n🎉 [导航测试] 所有页面导航测试完成！');
    
  } catch (error) {
    console.log(`❌ [导航测试] 测试过程中发生严重错误: ${error.message}`);
  } finally {
    console.log('⏰ [导航测试] 浏览器将在20秒后关闭...');
    await page.waitForTimeout(20000);
    await browser.close();
  }
}

// 运行导航测试
testPageNavigation().catch(console.error);
