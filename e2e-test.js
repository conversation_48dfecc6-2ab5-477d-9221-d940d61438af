const { chromium } = require('playwright');

async function testFrontend() {
  console.log('🚀 [测试] 开始端到端测试...');
  
  // 启动浏览器
  const browser = await chromium.launch({ 
    headless: false,  // 显示浏览器窗口
    slowMo: 1000      // 每个操作间隔1秒，方便观察
  });
  
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 }
  });
  
  const page = await context.newPage();
  
  try {
    // 访问首页
    console.log('🌐 [测试] 访问首页 http://localhost:3000/');
    await page.goto('http://localhost:3000/');
    await page.waitForLoadState('networkidle');
    
    // 等待页面加载完成
    await page.waitForTimeout(2000);
    
    // 截图保存当前页面
    await page.screenshot({ path: 'homepage.png' });
    console.log('📸 [测试] 首页截图已保存');
    
    // 查找并点击页面上的可点击元素
    console.log('🔍 [测试] 查找页面上的可点击元素...');
    
    // 查找所有按钮
    const buttons = await page.locator('button').all();
    console.log(`📝 [测试] 找到 ${buttons.length} 个按钮`);
    
    for (let i = 0; i < buttons.length; i++) {
      const button = buttons[i];
      const isVisible = await button.isVisible();
      if (isVisible) {
        const text = await button.textContent();
        console.log(`🔘 [测试] 按钮 ${i + 1}: "${text}"`);
        
        try {
          // 点击按钮
          await button.click();
          console.log(`✅ [测试] 成功点击按钮: "${text}"`);
          
          // 等待页面响应
          await page.waitForTimeout(2000);
          
          // 截图记录点击后的状态
          await page.screenshot({ path: `after-click-button-${i + 1}.png` });
          
        } catch (error) {
          console.log(`❌ [错误] 点击按钮失败: "${text}" - ${error.message}`);
        }
      }
    }
    
    // 查找所有链接
    const links = await page.locator('a').all();
    console.log(`📝 [测试] 找到 ${links.length} 个链接`);
    
    for (let i = 0; i < Math.min(links.length, 3); i++) { // 只测试前3个链接
      const link = links[i];
      const isVisible = await link.isVisible();
      if (isVisible) {
        const text = await link.textContent();
        const href = await link.getAttribute('href');
        console.log(`🔗 [测试] 链接 ${i + 1}: "${text}" -> ${href}`);
        
        try {
          // 点击链接
          await link.click();
          console.log(`✅ [测试] 成功点击链接: "${text}"`);
          
          // 等待页面加载
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(2000);
          
          // 截图记录导航后的页面
          await page.screenshot({ path: `after-click-link-${i + 1}.png` });
          
          // 返回首页继续测试
          await page.goBack();
          await page.waitForTimeout(1000);
          
        } catch (error) {
          console.log(`❌ [错误] 点击链接失败: "${text}" - ${error.message}`);
        }
      }
    }
    
    // 查找输入框并测试
    const inputs = await page.locator('input').all();
    console.log(`📝 [测试] 找到 ${inputs.length} 个输入框`);
    
    for (let i = 0; i < inputs.length; i++) {
      const input = inputs[i];
      const isVisible = await input.isVisible();
      if (isVisible) {
        const placeholder = await input.getAttribute('placeholder') || '';
        const type = await input.getAttribute('type') || 'text';
        console.log(`📝 [测试] 输入框 ${i + 1}: type="${type}", placeholder="${placeholder}"`);
        
        try {
          // 在输入框中输入测试文本
          await input.fill('测试文本');
          console.log(`✅ [测试] 成功在输入框中输入文本`);
          
          await page.waitForTimeout(1000);
          
          // 清空输入框
          await input.fill('');
          
        } catch (error) {
          console.log(`❌ [错误] 输入框操作失败: ${error.message}`);
        }
      }
    }
    
    console.log('🎉 [测试] 端到端测试完成！');
    
  } catch (error) {
    console.log(`❌ [错误] 测试过程中发生错误: ${error.message}`);
  } finally {
    // 保持浏览器打开30秒，让您可以查看结果
    console.log('⏰ [测试] 浏览器将在30秒后关闭...');
    await page.waitForTimeout(30000);
    await browser.close();
  }
}

// 运行测试
testFrontend().catch(console.error);
